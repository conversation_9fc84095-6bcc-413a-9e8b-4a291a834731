import { defaultResponseInterceptor, RequestClient } from '@vben/request';

function getWitlabRequestClient() {
  const witlabRequestClient = new RequestClient({
    baseURL: import.meta.env.VITE_WITLAB_API_URL,
    responseReturn: 'data',
  });

  // 处理返回的响应数据格式
  witlabRequestClient.addResponseInterceptor(
    defaultResponseInterceptor({
      codeField: 'code',
      dataField: 'data',
      successCode: 200,
    }),
  );
  return witlabRequestClient;
}

const witlabRequestClient = getWitlabRequestClient();

export namespace WitLabApi {
  export interface WitLabRequestParams {
    scriptName: string;
    params: Array<any>;
  }

  export interface PaginatedResult<T> {
    items: T[];
    total: number;
  }

  export interface WitLabResponseResult<T = any> {
    result: T;
    success: boolean;
  }

  export interface DataSet {
    Tables: Array<{
      Rows: Array<any>;
    }>;
  }
}

type WitLabRequestType = 'DataSource' | 'ServerScript';

/**
 * 调用 WitLab API 的通用方法
 */
async function callWitlab<T>(
  type: WitLabRequestType,
  requestParams: WitLabApi.WitLabRequestParams,
): Promise<WitLabApi.WitLabResponseResult<T>> {
  return witlabRequestClient.post<WitLabApi.WitLabResponseResult<T>>(
    '/WitLab/v1/WitLab/Proxy',
    {
      type,
      ...requestParams,
    },
  );
}

/**
 * 调用服务器脚本
 */
export async function callServer<T = any>(
  scriptName: string,
  params: Array<any>,
): Promise<T> {
  const limsParams: WitLabApi.WitLabRequestParams = {
    scriptName,
    params,
  };
  const respData = await callWitlab<T>('ServerScript', limsParams);
  return respData.result;
}

/**
 * 获取数据集
 */
export async function getDataSet<T = any>(
  scriptName: string,
  params: Array<any>,
): Promise<WitLabApi.PaginatedResult<T>> {
  const limsParams: WitLabApi.WitLabRequestParams = {
    scriptName,
    params,
  };
  const response = await callWitlab<WitLabApi.DataSet>(
    'DataSource',
    limsParams,
  );
  const tableData: WitLabApi.PaginatedResult<T> = {
    items: response.result.Tables?.[0]?.Rows as T[],
    total: response.result.Tables?.[0]?.Rows?.length ?? 0,
  };
  return tableData;
}

/**
 * 获取不分页的数据集
 */
export async function getDataSetNoPage<T = any>(
  scriptName: string,
  params: Array<any>,
): Promise<T[]> {
  const dataSetResponse = await getDataSet<T>(scriptName, params);
  return dataSetResponse.items;
}

/**
 * 上传文件
 * @param file 文件
 * @returns STARDOC_ID
 */
export async function UploadWitlabFile(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  const response = await witlabRequestClient.post(
    '/WitLab/v1/WitLab/Upload',
    formData,
  );
  const starDocId = response.result;
  return starDocId;
}

/**
 * 上传临时文件
 * @param file 文件
 * @returns 文件ID
 */
export async function UploadWitlabTempFile(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  const response = await witlabRequestClient.post('/WitLab/Files', formData);
  return response.result;
}

/**
 * 下载文件
 * @param starDocId 文件ID
 * @param fileName 文件名
 */
export async function DownloadWitlabFile(starDocId: string, fileName?: string) {
  const response = await witlabRequestClient.get(
    `/WitLab/Files/v1/WitLab/DownloadFileById?starDocId=${starDocId}`,
    {
      // application/octet-stream
      responseReturn: 'raw',
      responseType: 'blob',
    },
  );

  downloadFileFromResponse(response, fileName, starDocId);
}

/**
 * 下载临时文件
 * @param fileId 文件ID
 * @param fileName 文件名
 */
export async function DownloadWitlabTempFile(
  fileId: string,
  fileName?: string,
) {
  const response = await witlabRequestClient.get(
    `/WitLab/Files/download/${fileId}`,
    {
      // application/octet-stream
      responseReturn: 'raw',
      responseType: 'blob',
    },
  );

  downloadFileFromResponse(response, fileName, fileId);
}

function downloadFileFromResponse(
  response: any,
  fileName?: string,
  fileId?: string,
) {
  const contentDisposition = response.headers['content-disposition'];
  let parsedFileName = fileName;

  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
    if (filenameMatch && filenameMatch[1]) {
      parsedFileName = filenameMatch[1];
    }
  }

  const url = window.URL.createObjectURL(response.data);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName ?? parsedFileName ?? fileId ?? '';
  document.body.append(link);
  link.click();
  link.remove();
  window.URL.revokeObjectURL(url);
}

export async function updateProvider(params: any) {
  const { $grid, column, row } = params;
  if (!row.ORIGREC || !$grid.props.params.tableName) {
    return;
  }
  if ($grid.isUpdateByRow(row, column.field)) {
    const controlId = $grid.props.params.limsControlId ?? '';
    const tableName = $grid.props.params.tableName;
    const updateType = (typeof row[column.field])?.[0]?.toUpperCase() ?? 'S';

    await callServer('Runtime_Support.WS_UpdateProvider', [
      controlId,
      tableName,
      [
        [
          column.field,
          row[column.field],
          updateType,
          $grid.props.params.editingRows.find(
            (item: { ORIGREC: number }) => item.ORIGREC === row.ORIGREC,
          )?.[column.field] ?? '',
        ],
      ],
      row.ORIGREC,
      null,
    ]);
  }
}

export async function DSFromString(xmlString: string) {
  return callServer('Enterprise_Server.DataSetSupport.DSFromString', [
    xmlString,
  ]);
}
